import requests
import json
import sys
import csv
import os
from datetime import datetime

def get_mac_address():
    """
    Fetch MAC address from network device via HTTP API.
    
    Returns:
        str: MAC address if successful, None if failed
    """
    url = "http://*************:8090/cgi-bin/netinfo?key=eth0"
    username = "admin"
    password = "admin"
    
    try:
        # Make HTTP GET request with basic auth
        response = requests.get(
            url, 
            auth=(username, password),
            timeout=10
        )
        
        # Check if request was successful
        response.raise_for_status()
        
        # Parse JSON response
        data = response.json()
        
        # Check for API error
        if data.get("error", True):
            print("API returned error status")
            return None
            
        # Extract MAC address
        eth0_data = data.get("data", {}).get("eth0", {})
        mac_address = eth0_data.get("eth0mac")
        
        if not mac_address:
            print("MAC address not found in response")
            return None
            
        return mac_address
        
    except requests.exceptions.RequestException as e:
        print(f"Network error: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON parsing error: {e}")
        return None
    except Key<PERSON>rror as e:
        print(f"Missing field in response: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None

def save_mac_to_csv(mac_address, filename="mac_addresses.csv"):
    """
    Save MAC address to CSV file with sequence number.
    
    Args:
        mac_address (str): MAC address to save
        filename (str): CSV filename
    """
    file_exists = os.path.exists(filename)
    
    # Determine next sequence number
    sequence_num = 1
    if file_exists:
        try:
            with open(filename, 'r', newline='', encoding='utf-8') as file:
                reader = csv.reader(file)
                rows = list(reader)
                if len(rows) > 1:  # Skip header
                    sequence_num = len(rows)  # Next sequence number
        except Exception as e:
            print(f"Error reading existing CSV: {e}")
    
    # Write to CSV
    try:
        with open(filename, 'a', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            
            # Write header if file is new
            if not file_exists:
                writer.writerow(['序号', 'MAC地址'])
            
            # Write MAC address with sequence number
            writer.writerow([sequence_num, mac_address])
            
        print(f"MAC地址已保存到 {filename}，序号: {sequence_num}")
        
    except Exception as e:
        print(f"保存CSV文件时出错: {e}")

def main():
    """Main function to execute MAC address retrieval and save to CSV."""
    mac_address = get_mac_address()
    
    if mac_address:
        print(f"MAC Address: {mac_address}")
        save_mac_to_csv(mac_address)
        return mac_address
    else:
        print("Failed to retrieve MAC address")
        sys.exit(1)

if __name__ == "__main__":
    main()
