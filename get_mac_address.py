import requests
import json
import sys
import csv
import os
from datetime import datetime

def get_mac_address():
    """
    Fetch MAC address from network device via HTTP API.
    
    Returns:
        str: MAC address if successful, None if failed
    """
    url = "http://*************:8090/cgi-bin/netinfo?key=eth0"
    username = "admin"
    password = "admin"
    
    try:
        # Make HTTP GET request with basic auth
        response = requests.get(
            url, 
            auth=(username, password),
            timeout=10
        )
        
        # Check if request was successful
        response.raise_for_status()
        
        # Parse JSON response
        data = response.json()
        
        # Check for API error
        if data.get("error", True):
            print("API returned error status")
            return None
            
        # Extract MAC address
        eth0_data = data.get("data", {}).get("eth0", {})
        mac_address = eth0_data.get("eth0mac")
        
        if not mac_address:
            print("MAC address not found in response")
            return None
            
        return mac_address
        
    except requests.exceptions.RequestException as e:
        print(f"Network error: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON parsing error: {e}")
        return None
    except Key<PERSON>rror as e:
        print(f"Missing field in response: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None

def check_duplicate_mac(mac_address, filename="mac_addresses.csv"):
    """
    Check if MAC address already exists in CSV file.

    Args:
        mac_address (str): MAC address to check
        filename (str): CSV filename

    Returns:
        bool: True if MAC address already exists, False otherwise
    """
    if not os.path.exists(filename):
        return False

    try:
        with open(filename, 'r', newline='', encoding='utf-8') as file:
            reader = csv.reader(file)
            next(reader, None)  # Skip header

            for row in reader:
                if len(row) >= 2 and row[1].strip().upper() == mac_address.strip().upper():
                    return True

        return False

    except Exception as e:
        print(f"检查重复MAC地址时出错: {e}")
        return False

def find_all_duplicates(filename="mac_addresses.csv"):
    """
    Find all duplicate MAC addresses in CSV file.

    Args:
        filename (str): CSV filename

    Returns:
        dict: Dictionary with MAC addresses as keys and list of sequence numbers as values
    """
    if not os.path.exists(filename):
        return {}

    mac_count = {}
    duplicates = {}

    try:
        with open(filename, 'r', newline='', encoding='utf-8') as file:
            reader = csv.reader(file)
            next(reader, None)  # Skip header

            for row in reader:
                if len(row) >= 2:
                    seq_num = row[0].strip()
                    mac_addr = row[1].strip().upper()

                    if mac_addr not in mac_count:
                        mac_count[mac_addr] = []
                    mac_count[mac_addr].append(seq_num)

        # Find duplicates
        for mac_addr, seq_nums in mac_count.items():
            if len(seq_nums) > 1:
                duplicates[mac_addr] = seq_nums

        return duplicates

    except Exception as e:
        print(f"查找重复MAC地址时出错: {e}")
        return {}

def save_mac_to_csv(mac_address, filename="mac_addresses.csv"):
    """
    Save MAC address to CSV file with sequence number.

    Args:
        mac_address (str): MAC address to save
        filename (str): CSV filename
    """
    file_exists = os.path.exists(filename)

    # Determine next sequence number
    sequence_num = 1
    if file_exists:
        try:
            with open(filename, 'r', newline='', encoding='utf-8') as file:
                reader = csv.reader(file)
                rows = list(reader)
                if len(rows) > 1:  # Skip header
                    sequence_num = len(rows)  # Next sequence number
        except Exception as e:
            print(f"Error reading existing CSV: {e}")

    # Write to CSV
    try:
        with open(filename, 'a', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)

            # Write header if file is new
            if not file_exists:
                writer.writerow(['序号', 'MAC地址'])

            # Write MAC address with sequence number
            writer.writerow([sequence_num, mac_address])

        print(f"MAC地址已保存到 {filename}，序号: {sequence_num}")

    except Exception as e:
        print(f"保存CSV文件时出错: {e}")

def main():
    """Main function to execute MAC address retrieval and save to CSV."""
    mac_address = get_mac_address()

    if mac_address:
        print(f"MAC Address: {mac_address}")

        # Check for duplicates before saving
        if check_duplicate_mac(mac_address):
            print(f"警告: MAC地址 {mac_address} 已存在于文件中!")
            print("是否仍要保存? (y/n): ", end="")
            user_input = input().strip().lower()
            if user_input not in ['y', 'yes', '是']:
                print("取消保存操作")
                return mac_address

        save_mac_to_csv(mac_address)

        # Perform duplicate check after saving
        print("\n执行重复检查...")
        duplicates = find_all_duplicates()

        if duplicates:
            print("发现重复的MAC地址:")
            for mac_addr, seq_nums in duplicates.items():
                print(f"  MAC地址: {mac_addr}")
                print(f"  出现在序号: {', '.join(seq_nums)}")
                print(f"  重复次数: {len(seq_nums)}")
                print()
        else:
            print("未发现重复的MAC地址")

        return mac_address
    else:
        print("Failed to retrieve MAC address")
        sys.exit(1)

def check_duplicates_only(filename="mac_addresses.csv"):
    """
    Only perform duplicate check without fetching new MAC address.

    Args:
        filename (str): CSV filename to check
    """
    print(f"检查文件 {filename} 中的重复MAC地址...")

    if not os.path.exists(filename):
        print(f"文件 {filename} 不存在")
        return

    duplicates = find_all_duplicates(filename)

    if duplicates:
        print("发现重复的MAC地址:")
        total_duplicates = 0
        for mac_addr, seq_nums in duplicates.iytems():
            print(f"  MAC地址: {mac_addr}")
            print(f"  出现在序号: {', '.join(seq_nums)}")
            print(f"  重复次数: {len(seq_nums)}")
            total_duplicates += len(seq_nums) - 1  # Count extra occurrences
            print()
        print(f"总共发现 {len(duplicates)} 个重复的MAC地址，共有 {total_duplicates} 个重复项")
    else:
        print("未发现重复的MAC地址")

if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--check-duplicates":
        check_duplicates_only()
    else:
        main()
