# MAC地址获取与重复检查工具

这个工具可以从网络设备获取MAC地址并保存到CSV文件中，同时提供重复检查功能。

## 功能特性

1. **MAC地址获取**: 从指定的网络设备API获取MAC地址
2. **CSV保存**: 将MAC地址按序号保存到CSV文件中
3. **重复检查**: 检查新获取的MAC地址是否已存在
4. **全面重复扫描**: 扫描整个CSV文件查找所有重复项
5. **用户交互**: 发现重复时询问用户是否继续保存

## 使用方法

### 1. 获取MAC地址并保存（带重复检查）

```bash
python get_mac_address.py
```

这个命令会：
- 从网络设备获取MAC地址
- 检查是否已存在相同的MAC地址
- 如果发现重复，询问用户是否继续保存
- 保存MAC地址到CSV文件
- 执行完整的重复检查并显示结果

### 2. 仅执行重复检查

```bash
python get_mac_address.py --check-duplicates
```

这个命令会：
- 扫描现有的CSV文件
- 查找所有重复的MAC地址
- 显示重复项的详细信息

### 3. 运行测试

```bash
python test_duplicate_check.py
```

运行测试脚本验证重复检查功能是否正常工作。

## 输出文件

### mac_addresses.csv
CSV文件包含两列：
- **序号**: 自动递增的序列号
- **MAC地址**: 获取到的MAC地址

示例：
```csv
序号,MAC地址
1,00:4C:0E:22:94:D4
2,00:1A:2B:3C:4D:5E
3,00:4C:0E:22:94:D4
```

## 重复检查功能

### 保存前检查
在保存新的MAC地址之前，系统会检查该地址是否已存在于文件中。如果存在，会提示用户：

```
警告: MAC地址 00:4C:0E:22:94:D4 已存在于文件中!
是否仍要保存? (y/n):
```

### 保存后检查
保存完成后，系统会自动执行完整的重复检查，显示所有重复项：

```
执行重复检查...
发现重复的MAC地址:
  MAC地址: 00:4C:0E:22:94:D4
  出现在序号: 1, 3
  重复次数: 2
```

### 独立重复检查
使用 `--check-duplicates` 参数可以单独执行重复检查：

```
检查文件 mac_addresses.csv 中的重复MAC地址...
发现重复的MAC地址:
  MAC地址: 00:4C:0E:22:94:D4
  出现在序号: 1, 3
  重复次数: 2

总共发现 1 个重复的MAC地址，共有 1 个重复项
```

## 配置

### 网络设备配置
在 `get_mac_address.py` 中修改以下参数：

```python
url = "http://*************:8090/cgi-bin/netinfo?key=eth0"
username = "admin"
password = "admin"
```

### CSV文件名
默认文件名为 `mac_addresses.csv`，可以通过修改函数参数来更改。

## 错误处理

工具包含完善的错误处理机制：
- 网络连接错误
- JSON解析错误
- 文件读写错误
- API响应错误

所有错误都会显示详细的错误信息，帮助用户诊断问题。

## 依赖项

- `requests`: HTTP请求库
- `csv`: CSV文件处理（Python标准库）
- `os`: 文件系统操作（Python标准库）
- `sys`: 系统功能（Python标准库）
- `datetime`: 日期时间处理（Python标准库）

安装依赖：
```bash
pip install requests
```
