#!/usr/bin/env python3
"""
Test script for MAC address duplicate checking functionality.
"""

import os
import csv
import tempfile
from get_mac_address import check_duplicate_mac, find_all_duplicates

def test_duplicate_check():
    """Test the duplicate checking functionality."""
    
    # Create a temporary test CSV file
    test_data = [
        ['序号', 'MAC地址'],
        ['1', '00:4C:0E:22:94:D4'],
        ['2', '00:4C:0E:22:94:D4'],  # Duplicate
        ['3', '00:1A:2B:3C:4D:5E'],
        ['4', '00:1A:2B:3C:4D:5E'],  # Duplicate
        ['5', '00:1A:2B:3C:4D:5E'],  # Another duplicate
        ['6', 'AA:BB:CC:DD:EE:FF'],  # Unique
    ]
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as temp_file:
        writer = csv.writer(temp_file)
        writer.writerows(test_data)
        temp_filename = temp_file.name
    
    try:
        print("测试重复检查功能...")
        print(f"测试文件: {temp_filename}")
        print()
        
        # Test individual MAC check
        print("1. 测试单个MAC地址检查:")
        test_cases = [
            ('00:4C:0E:22:94:D4', True),   # Should exist (appears twice)
            ('00:1A:2B:3C:4D:5E', True),   # Should exist (appears three times)
            ('AA:BB:CC:DD:EE:FF', True),   # Should exist (appears once)
            ('11:22:33:44:55:66', False),  # Should not exist
        ]

        for mac, expected in test_cases:
            result = check_duplicate_mac(mac, temp_filename)
            status = "✓" if result == expected else "✗"
            print(f"  {status} MAC {mac}: {'存在' if result else '不存在'} (期望: {'存在' if expected else '不存在'})")
        
        print()
        
        # Test finding all duplicates
        print("2. 测试查找所有重复项:")
        duplicates = find_all_duplicates(temp_filename)
        
        expected_duplicates = {
            '00:4C:0E:22:94:D4': ['1', '2'],
            '00:1A:2B:3C:4D:5E': ['3', '4', '5']
        }
        
        print(f"发现 {len(duplicates)} 个重复的MAC地址:")
        for mac_addr, seq_nums in duplicates.items():
            print(f"  MAC地址: {mac_addr}")
            print(f"  出现在序号: {', '.join(seq_nums)}")
            print(f"  重复次数: {len(seq_nums)}")
            
            # Verify against expected
            if mac_addr in expected_duplicates:
                if seq_nums == expected_duplicates[mac_addr]:
                    print("  ✓ 结果正确")
                else:
                    print("  ✗ 结果不正确")
            else:
                print("  ✗ 意外的重复项")
            print()
        
        # Check if we found all expected duplicates
        for expected_mac in expected_duplicates:
            if expected_mac not in duplicates:
                print(f"  ✗ 未找到期望的重复项: {expected_mac}")
        
        print("测试完成!")
        
    finally:
        # Clean up temporary file
        os.unlink(temp_filename)

if __name__ == "__main__":
    test_duplicate_check()
